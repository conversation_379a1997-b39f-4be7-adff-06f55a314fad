import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:uuid/uuid.dart';
import '../../../../core/errors/exceptions.dart';
import '../../../../shared/enums/common_enums.dart';
import '../../../../shared/models/pagination.dart';
import '../../domain/entities/munda_record.dart';
import '../../domain/entities/munda_task.dart';
import '../../domain/entities/munda_requests.dart';

/// Firebase data source for Munda operations
class MundaFirebaseDataSource {
  final FirebaseFirestore _firestore;
  final Uuid _uuid;

  // Collection names
  static const String _recordsCollection = 'munda_records';
  static const String _tasksCollection = 'munda_tasks';
  static const String _serialNumbersCollection = 'munda_serial_numbers';

  MundaFirebaseDataSource({
    FirebaseFirestore? firestore,
    Uuid? uuid,
  })  : _firestore = firestore ?? FirebaseFirestore.instance,
        _uuid = uuid ?? const Uuid();

  // ==================== MUNDA RECORDS ====================

  /// Create a new Munda record
  Future<MundaRecord> createRecord(
    CreateMundaRecordRequest request,
    String userId,
  ) async {
    try {
      final recordId = _uuid.v4();
      final serialNumber = await _getNextSerialNumber(userId);
      final now = DateTime.now();

      final record = MundaRecord(
        id: recordId,
        createdAt: now,
        updatedAt: now,
        createdBy: userId,
        updatedBy: userId,
        serialNumber: serialNumber,
        entryDate: request.entryDate,
        designNumber: request.designNumber,
        lotNumber: request.lotNumber,
        rollNumber: request.rollNumber,
        threadType: request.threadType,
        customThread: request.customThread,
        pieces: request.pieces,
        rate: request.rate,
        amount: request.amount,
        checkedBy: request.checkedBy,
        remarks: request.remarks,
        taskId: request.taskId,
        userId: userId,
        status: CommonStatus.active,
      );

      await _firestore
          .collection(_recordsCollection)
          .doc(recordId)
          .set(record.toFirestore());

      // Update task progress if record is associated with a task
      if (request.taskId != null) {
        await _updateTaskProgress(request.taskId!);
      }

      return record;
    } catch (e) {
      throw DatabaseException('Failed to create Munda record: $e');
    }
  }

  /// Get a Munda record by ID
  Future<MundaRecord> getRecord(String recordId) async {
    try {
      final doc = await _firestore
          .collection(_recordsCollection)
          .doc(recordId)
          .get();

      if (!doc.exists) {
        throw NotFoundException('Munda record not found');
      }

      return MundaRecord.fromFirestore(doc.id, doc.data()!);
    } catch (e) {
      if (e is NotFoundException) rethrow;
      throw DatabaseException('Failed to get Munda record: $e');
    }
  }

  /// Update a Munda record
  Future<MundaRecord> updateRecord(
    UpdateMundaRecordRequest request,
    String userId,
  ) async {
    try {
      final docRef = _firestore
          .collection(_recordsCollection)
          .doc(request.recordId);

      final doc = await docRef.get();
      if (!doc.exists) {
        throw NotFoundException('Munda record not found');
      }

      final currentRecord = MundaRecord.fromFirestore(doc.id, doc.data()!);
      
      // Calculate new amount if pieces or rate changed
      final newPieces = request.pieces ?? currentRecord.pieces;
      final newRate = request.rate ?? currentRecord.rate;
      final newAmount = newPieces * newRate;

      final updateData = <String, dynamic>{
        'updatedAt': DateTime.now(),
        'updatedBy': userId,
        'version': currentRecord.version + 1,
      };

      if (request.entryDate != null) updateData['entryDate'] = request.entryDate;
      if (request.designNumber != null) updateData['designNumber'] = request.designNumber;
      if (request.lotNumber != null) updateData['lotNumber'] = request.lotNumber;
      if (request.rollNumber != null) updateData['rollNumber'] = request.rollNumber;
      if (request.threadType != null) updateData['threadType'] = request.threadType!.value;
      if (request.customThread != null) updateData['customThread'] = request.customThread;
      if (request.pieces != null) updateData['pieces'] = request.pieces;
      if (request.rate != null) updateData['rate'] = request.rate;
      if (request.checkedBy != null) updateData['checkedBy'] = request.checkedBy;
      if (request.remarks != null) updateData['remarks'] = request.remarks;
      if (request.status != null) updateData['status'] = request.status!.value;

      // Update amount if pieces or rate changed
      if (request.pieces != null || request.rate != null) {
        updateData['amount'] = newAmount;
      }

      await docRef.update(updateData);

      // Get updated record
      final updatedDoc = await docRef.get();
      final updatedRecord = MundaRecord.fromFirestore(updatedDoc.id, updatedDoc.data()!);

      // Update task progress if record is associated with a task
      if (updatedRecord.taskId != null) {
        await _updateTaskProgress(updatedRecord.taskId!);
      }

      return updatedRecord;
    } catch (e) {
      if (e is NotFoundException) rethrow;
      throw DatabaseException('Failed to update Munda record: $e');
    }
  }

  /// Delete a Munda record (soft delete)
  Future<void> deleteRecord(String recordId, String userId) async {
    try {
      final docRef = _firestore
          .collection(_recordsCollection)
          .doc(recordId);

      final doc = await docRef.get();
      if (!doc.exists) {
        throw NotFoundException('Munda record not found');
      }

      final record = MundaRecord.fromFirestore(doc.id, doc.data()!);

      await docRef.update({
        'deletedAt': DateTime.now(),
        'deletedBy': userId,
        'updatedAt': DateTime.now(),
        'updatedBy': userId,
        'version': record.version + 1,
      });

      // Update task progress if record was associated with a task
      if (record.taskId != null) {
        await _updateTaskProgress(record.taskId!);
      }
    } catch (e) {
      if (e is NotFoundException) rethrow;
      throw DatabaseException('Failed to delete Munda record: $e');
    }
  }

  /// Get paginated list of Munda records
  Future<PaginatedResult<MundaRecord>> getRecords({
    MundaRecordFilterCriteria? filter,
    PaginationParams? pagination,
    String? userId,
  }) async {
    try {
      Query query = _firestore.collection(_recordsCollection);

      // Apply filters
      if (filter != null) {
        query = _applyRecordFilters(query, filter);
      }

      // Filter by user if specified
      if (userId != null) {
        query = query.where('userId', isEqualTo: userId);
      }

      // Filter out deleted records
      query = query.where('deletedAt', isNull: true);

      // Apply sorting
      final sortBy = pagination?.sortBy ?? 'createdAt';
      final sortOrder = pagination?.sortOrder == 'asc';
      query = query.orderBy(sortBy, descending: !sortOrder);

      // Get total count
      final countSnapshot = await query.count().get();
      final total = countSnapshot.count ?? 0;

      // Apply pagination
      final perPage = pagination?.perPage ?? 20;
      final page = pagination?.page ?? 1;
      final offset = (page - 1) * perPage;

      if (offset > 0) {
        query = query.offset(offset);
      }
      query = query.limit(perPage);

      final snapshot = await query.get();
      final records = snapshot.docs
          .map((doc) => MundaRecord.fromFirestore(doc.id, doc.data() as Map<String, dynamic>))
          .toList();

      return PaginatedResult.create(
        data: records,
        currentPage: page,
        perPage: perPage,
        total: total,
      );
    } catch (e) {
      throw DatabaseException('Failed to get Munda records: $e');
    }
  }

  /// Get records for a specific task
  Future<List<MundaRecord>> getRecordsByTask(String taskId) async {
    try {
      final snapshot = await _firestore
          .collection(_recordsCollection)
          .where('taskId', isEqualTo: taskId)
          .where('deletedAt', isNull: true)
          .orderBy('serialNumber')
          .get();

      return snapshot.docs
          .map((doc) => MundaRecord.fromFirestore(doc.id, doc.data()))
          .toList();
    } catch (e) {
      throw DatabaseException('Failed to get records by task: $e');
    }
  }

  /// Get summary of records
  Future<MundaRecordSummary> getRecordsSummary({
    MundaRecordFilterCriteria? filter,
    String? userId,
  }) async {
    try {
      Query query = _firestore.collection(_recordsCollection);

      // Apply filters
      if (filter != null) {
        query = _applyRecordFilters(query, filter);
      }

      // Filter by user if specified
      if (userId != null) {
        query = query.where('userId', isEqualTo: userId);
      }

      // Filter out deleted records
      query = query.where('deletedAt', isNull: true);

      final snapshot = await query.get();
      
      if (snapshot.docs.isEmpty) {
        return MundaRecordSummary.empty();
      }

      int totalRolls = 0;
      int totalPieces = 0;
      double totalAmount = 0.0;

      for (final doc in snapshot.docs) {
        final data = doc.data() as Map<String, dynamic>;
        totalRolls++;
        totalPieces += (data['pieces'] as int? ?? 0);
        totalAmount += (data['amount'] as num? ?? 0).toDouble();
      }

      return MundaRecordSummary(
        totalRolls: totalRolls,
        totalPieces: totalPieces,
        totalAmount: totalAmount,
        fromDate: filter?.fromDate,
        toDate: filter?.toDate,
        userId: userId,
      );
    } catch (e) {
      throw DatabaseException('Failed to get records summary: $e');
    }
  }

  /// Get next serial number for a user
  Future<int> getNextSerialNumber(String userId) async {
    return await _getNextSerialNumber(userId);
  }

  // ==================== HELPER METHODS ====================

  /// Apply filters to records query
  Query _applyRecordFilters(Query query, MundaRecordFilterCriteria filter) {
    if (filter.fromDate != null) {
      query = query.where('entryDate', isGreaterThanOrEqualTo: filter.fromDate);
    }
    if (filter.toDate != null) {
      query = query.where('entryDate', isLessThanOrEqualTo: filter.toDate);
    }
    if (filter.designNumber != null) {
      query = query.where('designNumber', isEqualTo: filter.designNumber);
    }
    if (filter.lotNumber != null) {
      query = query.where('lotNumber', isEqualTo: filter.lotNumber);
    }
    if (filter.rollNumber != null) {
      query = query.where('rollNumber', isEqualTo: filter.rollNumber);
    }
    if (filter.threadType != null) {
      query = query.where('threadType', isEqualTo: filter.threadType!.value);
    }
    if (filter.checkedBy != null) {
      query = query.where('checkedBy', isEqualTo: filter.checkedBy);
    }
    if (filter.taskId != null) {
      query = query.where('taskId', isEqualTo: filter.taskId);
    }
    if (filter.status != null) {
      query = query.where('status', isEqualTo: filter.status!.value);
    }

    return query;
  }

  /// Get and increment serial number for a user
  Future<int> _getNextSerialNumber(String userId) async {
    final docRef = _firestore
        .collection(_serialNumbersCollection)
        .doc(userId);

    return await _firestore.runTransaction<int>((transaction) async {
      final doc = await transaction.get(docRef);
      
      int nextSerial = 1;
      if (doc.exists) {
        nextSerial = (doc.data()?['lastSerial'] as int? ?? 0) + 1;
      }

      transaction.set(docRef, {
        'lastSerial': nextSerial,
        'updatedAt': DateTime.now(),
      }, SetOptions(merge: true));

      return nextSerial;
    });
  }

  /// Update task progress based on associated records
  Future<void> _updateTaskProgress(String taskId) async {
    try {
      final records = await getRecordsByTask(taskId);

      int actualRecords = records.length;
      int actualPieces = records.fold(0, (sum, record) => sum + record.pieces);
      double actualAmount = records.fold(0.0, (sum, record) => sum + record.amount);

      await _firestore
          .collection(_tasksCollection)
          .doc(taskId)
          .update({
        'actualRecords': actualRecords,
        'actualPieces': actualPieces,
        'actualAmount': actualAmount,
        'updatedAt': DateTime.now(),
      });
    } catch (e) {
      // Log error but don't throw to avoid breaking the main operation
      print('Failed to update task progress: $e');
    }
  }

  // ==================== MUNDA TASKS ====================

  /// Create a new Munda task
  Future<MundaTask> createTask(
    CreateMundaTaskRequest request,
    String createdBy,
  ) async {
    try {
      final taskId = _uuid.v4();
      final now = DateTime.now();

      final task = MundaTask(
        id: taskId,
        createdAt: now,
        updatedAt: now,
        createdBy: createdBy,
        updatedBy: createdBy,
        title: request.title,
        description: request.description,
        status: MundaTaskStatus.pending,
        priority: request.priority,
        assignedTo: request.assignedTo,
        assignedAt: request.assignedTo != null ? now : null,
        assignedBy: request.assignedTo != null ? createdBy : null,
        dueDate: request.dueDate,
        expectedRecords: request.expectedRecords,
        targetPieces: request.targetPieces,
        targetAmount: request.targetAmount,
        orderId: request.orderId,
        productionBatchId: request.productionBatchId,
        designNumbers: request.designNumbers,
        lotNumbers: request.lotNumbers,
        instructions: request.instructions,
      );

      await _firestore
          .collection(_tasksCollection)
          .doc(taskId)
          .set(task.toFirestore());

      return task;
    } catch (e) {
      throw DatabaseException('Failed to create Munda task: $e');
    }
  }

  /// Get a Munda task by ID
  Future<MundaTask> getTask(String taskId) async {
    try {
      final doc = await _firestore
          .collection(_tasksCollection)
          .doc(taskId)
          .get();

      if (!doc.exists) {
        throw NotFoundException('Munda task not found');
      }

      return MundaTask.fromFirestore(doc.id, doc.data()!);
    } catch (e) {
      if (e is NotFoundException) rethrow;
      throw DatabaseException('Failed to get Munda task: $e');
    }
  }

  /// Update a Munda task
  Future<MundaTask> updateTask(
    UpdateMundaTaskRequest request,
    String updatedBy,
  ) async {
    try {
      final docRef = _firestore
          .collection(_tasksCollection)
          .doc(request.taskId);

      final doc = await docRef.get();
      if (!doc.exists) {
        throw NotFoundException('Munda task not found');
      }

      final currentTask = MundaTask.fromFirestore(doc.id, doc.data()!);

      final updateData = <String, dynamic>{
        'updatedAt': DateTime.now(),
        'updatedBy': updatedBy,
        'version': currentTask.version + 1,
      };

      if (request.title != null) updateData['title'] = request.title;
      if (request.description != null) updateData['description'] = request.description;
      if (request.status != null) {
        updateData['status'] = request.status!.value;

        // Update timestamps based on status changes
        if (request.status == MundaTaskStatus.inProgress && currentTask.startedAt == null) {
          updateData['startedAt'] = DateTime.now();
        } else if (request.status == MundaTaskStatus.completed && currentTask.completedAt == null) {
          updateData['completedAt'] = DateTime.now();
        } else if (request.status == MundaTaskStatus.sentToBundling && currentTask.sentToBundlingAt == null) {
          updateData['sentToBundlingAt'] = DateTime.now();
        }
      }
      if (request.priority != null) updateData['priority'] = request.priority!.value;
      if (request.assignedTo != null) {
        updateData['assignedTo'] = request.assignedTo;
        if (currentTask.assignedTo != request.assignedTo) {
          updateData['assignedAt'] = DateTime.now();
          updateData['assignedBy'] = updatedBy;
        }
      }
      if (request.dueDate != null) updateData['dueDate'] = request.dueDate;
      if (request.expectedRecords != null) updateData['expectedRecords'] = request.expectedRecords;
      if (request.targetPieces != null) updateData['targetPieces'] = request.targetPieces;
      if (request.targetAmount != null) updateData['targetAmount'] = request.targetAmount;
      if (request.instructions != null) updateData['instructions'] = request.instructions;
      if (request.operatorNotes != null) updateData['operatorNotes'] = request.operatorNotes;
      if (request.qualityNotes != null) updateData['qualityNotes'] = request.qualityNotes;

      await docRef.update(updateData);

      // Get updated task
      final updatedDoc = await docRef.get();
      return MundaTask.fromFirestore(updatedDoc.id, updatedDoc.data()!);
    } catch (e) {
      if (e is NotFoundException) rethrow;
      throw DatabaseException('Failed to update Munda task: $e');
    }
  }

  /// Delete a Munda task (soft delete)
  Future<void> deleteTask(String taskId, String deletedBy) async {
    try {
      final docRef = _firestore
          .collection(_tasksCollection)
          .doc(taskId);

      final doc = await docRef.get();
      if (!doc.exists) {
        throw NotFoundException('Munda task not found');
      }

      final task = MundaTask.fromFirestore(doc.id, doc.data()!);

      await docRef.update({
        'deletedAt': DateTime.now(),
        'deletedBy': deletedBy,
        'updatedAt': DateTime.now(),
        'updatedBy': deletedBy,
        'version': task.version + 1,
      });
    } catch (e) {
      if (e is NotFoundException) rethrow;
      throw DatabaseException('Failed to delete Munda task: $e');
    }
  }

  /// Get paginated list of Munda tasks
  Future<PaginatedResult<MundaTask>> getTasks({
    MundaTaskFilterCriteria? filter,
    PaginationParams? pagination,
  }) async {
    try {
      Query query = _firestore.collection(_tasksCollection);

      // Apply filters
      if (filter != null) {
        query = _applyTaskFilters(query, filter);
      }

      // Filter out deleted tasks
      query = query.where('deletedAt', isNull: true);

      // Apply sorting
      final sortBy = pagination?.sortBy ?? 'createdAt';
      final sortOrder = pagination?.sortOrder == 'asc';
      query = query.orderBy(sortBy, descending: !sortOrder);

      // Get total count
      final countSnapshot = await query.count().get();
      final total = countSnapshot.count ?? 0;

      // Apply pagination
      final perPage = pagination?.perPage ?? 20;
      final page = pagination?.page ?? 1;
      final offset = (page - 1) * perPage;

      if (offset > 0) {
        query = query.offset(offset);
      }
      query = query.limit(perPage);

      final snapshot = await query.get();
      final tasks = snapshot.docs
          .map((doc) => MundaTask.fromFirestore(doc.id, doc.data() as Map<String, dynamic>))
          .toList();

      return PaginatedResult.create(
        data: tasks,
        currentPage: page,
        perPage: perPage,
        total: total,
      );
    } catch (e) {
      throw DatabaseException('Failed to get Munda tasks: $e');
    }
  }

  /// Get tasks assigned to a specific user
  Future<List<MundaTask>> getTasksByUser(
    String userId, {
    MundaTaskFilterCriteria? filter,
  }) async {
    try {
      Query query = _firestore
          .collection(_tasksCollection)
          .where('assignedTo', isEqualTo: userId)
          .where('deletedAt', isNull: true);

      // Apply additional filters
      if (filter != null) {
        query = _applyTaskFilters(query, filter);
      }

      query = query.orderBy('createdAt', descending: true);

      final snapshot = await query.get();
      return snapshot.docs
          .map((doc) => MundaTask.fromFirestore(doc.id, doc.data() as Map<String, dynamic>))
          .toList();
    } catch (e) {
      throw DatabaseException('Failed to get tasks by user: $e');
    }
  }

  /// Apply filters to tasks query
  Query _applyTaskFilters(Query query, MundaTaskFilterCriteria filter) {
    if (filter.status != null) {
      query = query.where('status', isEqualTo: filter.status!.value);
    }
    if (filter.priority != null) {
      query = query.where('priority', isEqualTo: filter.priority!.value);
    }
    if (filter.assignedTo != null) {
      query = query.where('assignedTo', isEqualTo: filter.assignedTo);
    }
    if (filter.fromDate != null) {
      query = query.where('createdAt', isGreaterThanOrEqualTo: filter.fromDate);
    }
    if (filter.toDate != null) {
      query = query.where('createdAt', isLessThanOrEqualTo: filter.toDate);
    }
    if (filter.dueBefore != null) {
      query = query.where('dueDate', isLessThanOrEqualTo: filter.dueBefore);
    }
    if (filter.dueAfter != null) {
      query = query.where('dueDate', isGreaterThanOrEqualTo: filter.dueAfter);
    }
    if (filter.orderId != null) {
      query = query.where('orderId', isEqualTo: filter.orderId);
    }
    if (filter.productionBatchId != null) {
      query = query.where('productionBatchId', isEqualTo: filter.productionBatchId);
    }

    return query;
  }
}
