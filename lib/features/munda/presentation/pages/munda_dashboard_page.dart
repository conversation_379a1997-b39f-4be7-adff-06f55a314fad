import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../../shared/enums/common_enums.dart';
import '../../../auth/presentation/bloc/firebase_auth_bloc.dart';
import '../bloc/munda_bloc.dart';
import '../bloc/munda_event.dart';
import '../bloc/munda_state.dart';
import '../widgets/munda_app_bar.dart';
import '../widgets/munda_bottom_navigation.dart';
import '../widgets/munda_drawer.dart';
import 'munda_data_entry_page.dart';
import 'munda_tasks_page.dart';
import 'munda_records_page.dart';
import 'munda_analytics_page.dart';

/// Main dashboard page for Munda operations
class MundaDashboardPage extends StatefulWidget {
  const MundaDashboardPage({super.key});

  @override
  State<MundaDashboardPage> createState() => _MundaDashboardPageState();
}

class _MundaDashboardPageState extends State<MundaDashboardPage> {
  int _currentIndex = 0;
  final PageController _pageController = PageController();

  @override
  void initState() {
    super.initState();
    _loadInitialData();
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  void _loadInitialData() {
    final authState = context.read<FirebaseAuthBloc>().state;
    if (authState is FirebaseAuthSuccess) {
      final userId = authState.user.id;
      
      // Load user's tasks and recent records
      context.read<MundaBloc>().add(LoadUserTasks(userId: userId));
      context.read<MundaBloc>().add(LoadRecords(userId: userId));
      context.read<MundaBloc>().add(LoadRecordsSummary(userId: userId));
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<FirebaseAuthBloc, FirebaseAuthState>(
      listener: (context, state) {
        if (state is FirebaseAuthInitial) {
          // User signed out, navigate back to login
          Navigator.of(context).pushReplacementNamed('/munda-login');
        }
      },
      child: Scaffold(
        backgroundColor: AppColors.background,
        appBar: const MundaAppBar(),
        drawer: const MundaDrawer(),
        body: PageView(
          controller: _pageController,
          onPageChanged: (index) {
            setState(() {
              _currentIndex = index;
            });
          },
          children: const [
            MundaDashboardHome(),
            MundaDataEntryPage(),
            MundaTasksPage(),
            MundaRecordsPage(),
            MundaAnalyticsPage(),
          ],
        ),
        bottomNavigationBar: MundaBottomNavigation(
          currentIndex: _currentIndex,
          onTap: (index) {
            setState(() {
              _currentIndex = index;
            });
            _pageController.animateToPage(
              index,
              duration: const Duration(milliseconds: 300),
              curve: Curves.easeInOut,
            );
          },
        ),
      ),
    );
  }
}

/// Home tab content for the dashboard
class MundaDashboardHome extends StatelessWidget {
  const MundaDashboardHome({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<MundaBloc, MundaState>(
      builder: (context, state) {
        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Welcome Section
              _buildWelcomeSection(context),
              const SizedBox(height: 24),
              
              // Quick Stats
              _buildQuickStats(context, state),
              const SizedBox(height: 24),
              
              // Recent Tasks
              _buildRecentTasks(context, state),
              const SizedBox(height: 24),
              
              // Quick Actions
              _buildQuickActions(context),
            ],
          ),
        );
      },
    );
  }

  Widget _buildWelcomeSection(BuildContext context) {
    return BlocBuilder<FirebaseAuthBloc, FirebaseAuthState>(
      builder: (context, authState) {
        final userName = authState is FirebaseAuthSuccess 
            ? authState.user.displayName 
            : 'Operator';
            
        return Container(
          width: double.infinity,
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [AppColors.primary, AppColors.primary.withOpacity(0.8)],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.circular(16),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Welcome back, $userName!',
                style: AppTextStyles.headlineMedium.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Ready to manage your Munda operations',
                style: AppTextStyles.bodyLarge.copyWith(
                  color: Colors.white.withOpacity(0.9),
                ),
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Icon(
                    Icons.access_time,
                    color: Colors.white.withOpacity(0.9),
                    size: 16,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'Today, ${DateTime.now().toString().split(' ')[0]}',
                    style: AppTextStyles.bodyMedium.copyWith(
                      color: Colors.white.withOpacity(0.9),
                    ),
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildQuickStats(BuildContext context, MundaState state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Today\'s Summary',
          style: AppTextStyles.headlineSmall.copyWith(
            color: AppColors.textPrimary,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        
        if (state is RecordsLoaded && state.summary != null)
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  'Total Rolls',
                  state.summary!.totalRolls.toString(),
                  Icons.inventory_2,
                  AppColors.primary,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildStatCard(
                  'Total Pieces',
                  state.summary!.totalPieces.toString(),
                  Icons.straighten,
                  AppColors.secondary,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildStatCard(
                  'Total Amount',
                  '₹${state.summary!.totalAmount.toStringAsFixed(2)}',
                  Icons.currency_rupee,
                  AppColors.success,
                ),
              ),
            ],
          )
        else
          Row(
            children: [
              Expanded(child: _buildStatCard('Total Rolls', '0', Icons.inventory_2, AppColors.primary)),
              const SizedBox(width: 12),
              Expanded(child: _buildStatCard('Total Pieces', '0', Icons.straighten, AppColors.secondary)),
              const SizedBox(width: 12),
              Expanded(child: _buildStatCard('Total Amount', '₹0.00', Icons.currency_rupee, AppColors.success)),
            ],
          ),
      ],
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Icon(
            icon,
            color: color,
            size: 24,
          ),
          const SizedBox(height: 8),
          Text(
            value,
            style: AppTextStyles.headlineSmall.copyWith(
              color: AppColors.textPrimary,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: AppTextStyles.bodySmall.copyWith(
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildRecentTasks(BuildContext context, MundaState state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Recent Tasks',
              style: AppTextStyles.headlineSmall.copyWith(
                color: AppColors.textPrimary,
                fontWeight: FontWeight.bold,
              ),
            ),
            TextButton(
              onPressed: () {
                // Navigate to tasks page
              },
              child: Text(
                'View All',
                style: AppTextStyles.bodyMedium.copyWith(
                  color: AppColors.primary,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        
        if (state is TasksLoaded && state.userTasks.isNotEmpty)
          ...state.userTasks.take(3).map((task) => _buildTaskCard(task))
        else
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 10,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              children: [
                Icon(
                  Icons.task_alt,
                  color: AppColors.textSecondary,
                  size: 48,
                ),
                const SizedBox(height: 16),
                Text(
                  'No tasks assigned',
                  style: AppTextStyles.bodyLarge.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          ),
      ],
    );
  }

  Widget _buildTaskCard(task) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            width: 8,
            height: 40,
            decoration: BoxDecoration(
              color: _getStatusColor(task.status),
              borderRadius: BorderRadius.circular(4),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  task.title,
                  style: AppTextStyles.bodyLarge.copyWith(
                    color: AppColors.textPrimary,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  task.status.displayName,
                  style: AppTextStyles.bodySmall.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          ),
          Icon(
            Icons.chevron_right,
            color: AppColors.textSecondary,
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(MundaTaskStatus status) {
    switch (status) {
      case MundaTaskStatus.pending:
        return AppColors.warning;
      case MundaTaskStatus.assigned:
        return AppColors.info;
      case MundaTaskStatus.inProgress:
        return AppColors.primary;
      case MundaTaskStatus.completed:
        return AppColors.success;
      case MundaTaskStatus.sentToBundling:
        return AppColors.secondary;
      case MundaTaskStatus.cancelled:
        return AppColors.error;
    }
  }

  Widget _buildQuickActions(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Quick Actions',
          style: AppTextStyles.headlineSmall.copyWith(
            color: AppColors.textPrimary,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        
        Row(
          children: [
            Expanded(
              child: _buildActionCard(
                'New Entry',
                'Add new record',
                Icons.add_circle_outline,
                AppColors.primary,
                () {
                  // Navigate to data entry
                },
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildActionCard(
                'View Records',
                'Browse all records',
                Icons.list_alt,
                AppColors.secondary,
                () {
                  // Navigate to records
                },
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildActionCard(
    String title,
    String subtitle,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          children: [
            Icon(
              icon,
              color: color,
              size: 32,
            ),
            const SizedBox(height: 12),
            Text(
              title,
              style: AppTextStyles.bodyLarge.copyWith(
                color: AppColors.textPrimary,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              subtitle,
              style: AppTextStyles.bodySmall.copyWith(
                color: AppColors.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
