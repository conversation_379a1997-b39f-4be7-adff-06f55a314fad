import 'dart:async';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../shared/models/pagination.dart';
import '../../domain/repositories/munda_repository.dart';
import 'munda_event.dart';
import 'munda_state.dart';

/// BLoC for managing Munda operations
class MundaBloc extends Bloc<MundaEvent, MundaState> {
  final MundaRepository _repository;

  MundaBloc({
    required MundaRepository repository,
  })  : _repository = repository,
        super(const MundaInitial()) {
    // Register event handlers
    on<LoadRecords>(_onLoadRecords);
    on<LoadMoreRecords>(_onLoadMoreRecords);
    on<CreateRecord>(_onCreateRecord);
    on<UpdateRecord>(_onUpdateRecord);
    on<DeleteRecord>(_onDeleteRecord);
    on<LoadRecordsSummary>(_onLoadRecordsSummary);
    on<ApplyRecordFilter>(_onApplyRecordFilter);
    on<ClearRecordFilter>(_onClearRecordFilter);
    on<SearchRecords>(_onSearchRecords);
    
    on<LoadTasks>(_onLoadTasks);
    on<LoadUserTasks>(_onLoadUserTasks);
    on<CreateTask>(_onCreateTask);
    on<UpdateTask>(_onUpdateTask);
    on<AssignTask>(_onAssignTask);
    on<StartTask>(_onStartTask);
    on<CompleteTask>(_onCompleteTask);
    on<SendTaskToBundling>(_onSendTaskToBundling);
    on<CancelTask>(_onCancelTask);
    on<DeleteTask>(_onDeleteTask);
    on<ApplyTaskFilter>(_onApplyTaskFilter);
    on<ClearTaskFilter>(_onClearTaskFilter);
    on<SearchTasks>(_onSearchTasks);
    
    on<ResetMundaState>(_onResetMundaState);
    on<RefreshAllData>(_onRefreshAllData);
    on<SyncOfflineData>(_onSyncOfflineData);
    on<LoadAnalytics>(_onLoadAnalytics);
  }

  // ==================== RECORD EVENT HANDLERS ====================

  Future<void> _onLoadRecords(LoadRecords event, Emitter<MundaState> emit) async {
    if (!event.refresh && state is RecordsLoaded) {
      // If not refreshing and already have data, just update loading state
      final currentState = state as RecordsLoaded;
      emit(currentState.copyWith(isLoadingMore: false));
    } else {
      emit(const MundaLoading(message: 'Loading records...'));
    }

    final result = await _repository.getRecords(
      filter: event.filter,
      pagination: event.pagination,
      userId: event.userId,
    );

    result.fold(
      (failure) => emit(MundaError(failure: failure, context: 'Loading records')),
      (records) {
        // Also load summary if needed
        if (event.filter != null || event.userId != null) {
          _loadSummaryForRecords(event.filter, event.userId, emit, records);
        } else {
          emit(RecordsLoaded(
            records: records,
            currentFilter: event.filter,
            hasReachedMax: !records.hasMorePages,
          ));
        }
      },
    );
  }

  Future<void> _loadSummaryForRecords(
    filter,
    userId,
    Emitter<MundaState> emit,
    records,
  ) async {
    final summaryResult = await _repository.getRecordsSummary(
      filter: filter,
      userId: userId,
    );

    summaryResult.fold(
      (failure) => emit(RecordsLoaded(
        records: records,
        currentFilter: filter,
        hasReachedMax: !records.hasMorePages,
      )),
      (summary) => emit(RecordsLoaded(
        records: records,
        summary: summary,
        currentFilter: filter,
        hasReachedMax: !records.hasMorePages,
      )),
    );
  }

  Future<void> _onLoadMoreRecords(LoadMoreRecords event, Emitter<MundaState> emit) async {
    if (state is! RecordsLoaded) return;

    final currentState = state as RecordsLoaded;
    if (currentState.hasReachedMax || currentState.isLoadingMore) return;

    emit(currentState.copyWith(isLoadingMore: true));

    final nextPage = currentState.records.pagination.currentPage + 1;
    final pagination = PaginationParams(
      page: nextPage,
      perPage: currentState.records.pagination.perPage,
    );

    final result = await _repository.getRecords(
      filter: currentState.currentFilter,
      pagination: pagination,
    );

    result.fold(
      (failure) => emit(currentState.copyWith(isLoadingMore: false)),
      (newRecords) => emit(currentState.addMoreRecords(newRecords)),
    );
  }

  Future<void> _onCreateRecord(CreateRecord event, Emitter<MundaState> emit) async {
    emit(const MundaLoading(message: 'Creating record...'));

    final result = await _repository.createRecord(event.request, event.userId);

    result.fold(
      (failure) => emit(MundaError(failure: failure, context: 'Creating record')),
      (record) {
        emit(const MundaOperationSuccess(message: 'Record created successfully'));
        
        // Update the current state if we have records loaded
        if (state is RecordsLoaded) {
          final currentState = state as RecordsLoaded;
          emit(currentState.addRecord(record));
        }
      },
    );
  }

  Future<void> _onUpdateRecord(UpdateRecord event, Emitter<MundaState> emit) async {
    emit(const MundaLoading(message: 'Updating record...'));

    final result = await _repository.updateRecord(event.request, event.userId);

    result.fold(
      (failure) => emit(MundaError(failure: failure, context: 'Updating record')),
      (record) {
        emit(const MundaOperationSuccess(message: 'Record updated successfully'));
        
        // Update the current state if we have records loaded
        if (state is RecordsLoaded) {
          final currentState = state as RecordsLoaded;
          emit(currentState.updateRecord(record));
        }
      },
    );
  }

  Future<void> _onDeleteRecord(DeleteRecord event, Emitter<MundaState> emit) async {
    emit(const MundaLoading(message: 'Deleting record...'));

    final result = await _repository.deleteRecord(event.recordId, event.userId);

    result.fold(
      (failure) => emit(MundaError(failure: failure, context: 'Deleting record')),
      (_) {
        emit(const MundaOperationSuccess(message: 'Record deleted successfully'));
        
        // Update the current state if we have records loaded
        if (state is RecordsLoaded) {
          final currentState = state as RecordsLoaded;
          emit(currentState.removeRecord(event.recordId));
        }
      },
    );
  }

  Future<void> _onLoadRecordsSummary(LoadRecordsSummary event, Emitter<MundaState> emit) async {
    final result = await _repository.getRecordsSummary(
      filter: event.filter,
      userId: event.userId,
    );

    result.fold(
      (failure) => emit(MundaError(failure: failure, context: 'Loading summary')),
      (summary) {
        if (state is RecordsLoaded) {
          final currentState = state as RecordsLoaded;
          emit(currentState.copyWith(summary: summary));
        }
      },
    );
  }

  Future<void> _onApplyRecordFilter(ApplyRecordFilter event, Emitter<MundaState> emit) async {
    // Reload records with the new filter
    add(LoadRecords(filter: event.filter, refresh: true));
  }

  Future<void> _onClearRecordFilter(ClearRecordFilter event, Emitter<MundaState> emit) async {
    // Reload records without filter
    add(const LoadRecords(refresh: true));
  }

  Future<void> _onSearchRecords(SearchRecords event, Emitter<MundaState> emit) async {
    final filter = MundaRecordFilterCriteria(searchQuery: event.query);
    add(LoadRecords(filter: filter, refresh: true));
  }

  // ==================== TASK EVENT HANDLERS ====================

  Future<void> _onLoadTasks(LoadTasks event, Emitter<MundaState> emit) async {
    if (!event.refresh && state is TasksLoaded) {
      final currentState = state as TasksLoaded;
      emit(currentState.copyWith(isLoadingMore: false));
    } else {
      emit(const MundaLoading(message: 'Loading tasks...'));
    }

    final result = await _repository.getTasks(
      filter: event.filter,
      pagination: event.pagination,
    );

    result.fold(
      (failure) => emit(MundaError(failure: failure, context: 'Loading tasks')),
      (tasks) => emit(TasksLoaded(
        tasks: tasks,
        currentFilter: event.filter,
        hasReachedMax: !tasks.hasMorePages,
      )),
    );
  }

  Future<void> _onLoadUserTasks(LoadUserTasks event, Emitter<MundaState> emit) async {
    if (!event.refresh) {
      emit(const MundaLoading(message: 'Loading user tasks...'));
    }

    final result = await _repository.getTasksByUser(
      event.userId,
      filter: event.filter,
    );

    result.fold(
      (failure) => emit(MundaError(failure: failure, context: 'Loading user tasks')),
      (userTasks) {
        if (state is TasksLoaded) {
          final currentState = state as TasksLoaded;
          emit(currentState.copyWith(userTasks: userTasks));
        } else {
          emit(TasksLoaded(
            tasks: PaginatedResult.empty(),
            userTasks: userTasks,
            currentFilter: event.filter,
          ));
        }
      },
    );
  }

  Future<void> _onCreateTask(CreateTask event, Emitter<MundaState> emit) async {
    emit(const MundaLoading(message: 'Creating task...'));

    final result = await _repository.createTask(event.request, event.createdBy);

    result.fold(
      (failure) => emit(MundaError(failure: failure, context: 'Creating task')),
      (task) {
        emit(const MundaOperationSuccess(message: 'Task created successfully'));
        
        // Update the current state if we have tasks loaded
        if (state is TasksLoaded) {
          final currentState = state as TasksLoaded;
          emit(currentState.addTask(task));
        }
      },
    );
  }

  Future<void> _onUpdateTask(UpdateTask event, Emitter<MundaState> emit) async {
    emit(const MundaLoading(message: 'Updating task...'));

    final result = await _repository.updateTask(event.request, event.updatedBy);

    result.fold(
      (failure) => emit(MundaError(failure: failure, context: 'Updating task')),
      (task) {
        emit(const MundaOperationSuccess(message: 'Task updated successfully'));
        
        // Update the current state if we have tasks loaded
        if (state is TasksLoaded) {
          final currentState = state as TasksLoaded;
          emit(currentState.updateTask(task));
        }
      },
    );
  }

  Future<void> _onAssignTask(AssignTask event, Emitter<MundaState> emit) async {
    emit(const MundaLoading(message: 'Assigning task...'));

    final result = await _repository.assignTask(
      event.taskId,
      event.assignedTo,
      event.assignedBy,
    );

    result.fold(
      (failure) => emit(MundaError(failure: failure, context: 'Assigning task')),
      (task) {
        emit(const MundaOperationSuccess(message: 'Task assigned successfully'));
        
        if (state is TasksLoaded) {
          final currentState = state as TasksLoaded;
          emit(currentState.updateTask(task));
        }
      },
    );
  }

  Future<void> _onStartTask(StartTask event, Emitter<MundaState> emit) async {
    emit(const MundaLoading(message: 'Starting task...'));

    final result = await _repository.startTask(event.taskId, event.userId);

    result.fold(
      (failure) => emit(MundaError(failure: failure, context: 'Starting task')),
      (task) {
        emit(const MundaOperationSuccess(message: 'Task started successfully'));
        
        if (state is TasksLoaded) {
          final currentState = state as TasksLoaded;
          emit(currentState.updateTask(task));
        }
      },
    );
  }

  Future<void> _onCompleteTask(CompleteTask event, Emitter<MundaState> emit) async {
    emit(const MundaLoading(message: 'Completing task...'));

    final result = await _repository.completeTask(
      event.taskId,
      event.userId,
      notes: event.notes,
    );

    result.fold(
      (failure) => emit(MundaError(failure: failure, context: 'Completing task')),
      (task) {
        emit(const MundaOperationSuccess(message: 'Task completed successfully'));
        
        if (state is TasksLoaded) {
          final currentState = state as TasksLoaded;
          emit(currentState.updateTask(task));
        }
      },
    );
  }

  Future<void> _onSendTaskToBundling(SendTaskToBundling event, Emitter<MundaState> emit) async {
    emit(const MundaLoading(message: 'Sending task to bundling...'));

    final result = await _repository.sendTaskToBundling(
      event.taskId,
      event.userId,
      notes: event.notes,
    );

    result.fold(
      (failure) => emit(MundaError(failure: failure, context: 'Sending task to bundling')),
      (task) {
        emit(const MundaOperationSuccess(message: 'Task sent to bundling successfully'));
        
        if (state is TasksLoaded) {
          final currentState = state as TasksLoaded;
          emit(currentState.updateTask(task));
        }
      },
    );
  }

  Future<void> _onCancelTask(CancelTask event, Emitter<MundaState> emit) async {
    emit(const MundaLoading(message: 'Cancelling task...'));

    final result = await _repository.cancelTask(
      event.taskId,
      event.userId,
      event.reason,
    );

    result.fold(
      (failure) => emit(MundaError(failure: failure, context: 'Cancelling task')),
      (task) {
        emit(const MundaOperationSuccess(message: 'Task cancelled successfully'));
        
        if (state is TasksLoaded) {
          final currentState = state as TasksLoaded;
          emit(currentState.updateTask(task));
        }
      },
    );
  }

  Future<void> _onDeleteTask(DeleteTask event, Emitter<MundaState> emit) async {
    emit(const MundaLoading(message: 'Deleting task...'));

    final result = await _repository.deleteTask(event.taskId, event.deletedBy);

    result.fold(
      (failure) => emit(MundaError(failure: failure, context: 'Deleting task')),
      (_) {
        emit(const MundaOperationSuccess(message: 'Task deleted successfully'));
        
        if (state is TasksLoaded) {
          final currentState = state as TasksLoaded;
          emit(currentState.removeTask(event.taskId));
        }
      },
    );
  }

  Future<void> _onApplyTaskFilter(ApplyTaskFilter event, Emitter<MundaState> emit) async {
    add(LoadTasks(filter: event.filter, refresh: true));
  }

  Future<void> _onClearTaskFilter(ClearTaskFilter event, Emitter<MundaState> emit) async {
    add(const LoadTasks(refresh: true));
  }

  Future<void> _onSearchTasks(SearchTasks event, Emitter<MundaState> emit) async {
    final filter = MundaTaskFilterCriteria(searchQuery: event.query);
    add(LoadTasks(filter: filter, refresh: true));
  }

  // ==================== GENERAL EVENT HANDLERS ====================

  Future<void> _onResetMundaState(ResetMundaState event, Emitter<MundaState> emit) async {
    emit(const MundaInitial());
  }

  Future<void> _onRefreshAllData(RefreshAllData event, Emitter<MundaState> emit) async {
    emit(const MundaLoading(message: 'Refreshing data...'));
    
    // Reload both records and tasks
    add(const LoadRecords(refresh: true));
    add(const LoadTasks(refresh: true));
    
    if (event.userId != null) {
      add(LoadUserTasks(userId: event.userId!, refresh: true));
    }
  }

  Future<void> _onSyncOfflineData(SyncOfflineData event, Emitter<MundaState> emit) async {
    emit(const OfflineSyncInProgress(totalItems: 0, syncedItems: 0));

    final result = await _repository.syncOfflineData(event.userId);

    result.fold(
      (failure) => emit(MundaError(failure: failure, context: 'Syncing offline data')),
      (_) => emit(const OfflineSyncCompleted(syncedItems: 0)),
    );
  }

  Future<void> _onLoadAnalytics(LoadAnalytics event, Emitter<MundaState> emit) async {
    emit(const MundaLoading(message: 'Loading analytics...'));

    // Load various analytics data
    final userAnalyticsResult = await _repository.getUserProductivityAnalytics(
      event.userId ?? '',
      fromDate: event.fromDate,
      toDate: event.toDate,
    );

    final departmentAnalyticsResult = await _repository.getDepartmentAnalytics(
      fromDate: event.fromDate,
      toDate: event.toDate,
    );

    final qualityMetricsResult = await _repository.getQualityMetrics(
      userId: event.userId,
      fromDate: event.fromDate,
      toDate: event.toDate,
    );

    final taskStatisticsResult = await _repository.getTaskStatistics(
      userId: event.userId,
      fromDate: event.fromDate,
      toDate: event.toDate,
    );

    // Combine results
    final userAnalytics = userAnalyticsResult.fold(
      (failure) => <String, dynamic>{},
      (data) => data,
    );

    final departmentAnalytics = departmentAnalyticsResult.fold(
      (failure) => <String, dynamic>{},
      (data) => data,
    );

    final qualityMetrics = qualityMetricsResult.fold(
      (failure) => <String, dynamic>{},
      (data) => data,
    );

    final taskStatistics = taskStatisticsResult.fold(
      (failure) => <String, dynamic>{},
      (data) => data,
    );

    emit(AnalyticsLoaded(
      userAnalytics: userAnalytics,
      departmentAnalytics: departmentAnalytics,
      qualityMetrics: qualityMetrics,
      taskStatistics: taskStatistics,
    ));
  }
}
