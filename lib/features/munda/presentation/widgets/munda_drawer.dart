import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../auth/presentation/bloc/firebase_auth_bloc.dart';
import '../bloc/munda_bloc.dart';
import '../bloc/munda_event.dart';
import '../bloc/munda_state.dart';

/// Navigation drawer for Munda screens
class MundaDrawer extends StatelessWidget {
  const MundaDrawer({super.key});

  @override
  Widget build(BuildContext context) {
    return Drawer(
      backgroundColor: Colors.white,
      child: Column(
        children: [
          // Header
          _buildHeader(context),
          
          // Menu items
          Expanded(
            child: ListView(
              padding: EdgeInsets.zero,
              children: [
                _buildMenuItem(
                  icon: Icons.dashboard,
                  title: 'Dashboard',
                  onTap: () => _navigateToPage(context, 0),
                ),
                _buildMenuItem(
                  icon: Icons.add_circle_outline,
                  title: 'New Data Entry',
                  onTap: () => _navigateToPage(context, 1),
                ),
                _buildMenuItem(
                  icon: Icons.task_alt,
                  title: 'My Tasks',
                  onTap: () => _navigateToPage(context, 2),
                ),
                _buildMenuItem(
                  icon: Icons.list_alt,
                  title: 'All Records',
                  onTap: () => _navigateToPage(context, 3),
                ),
                _buildMenuItem(
                  icon: Icons.analytics,
                  title: 'Analytics',
                  onTap: () => _navigateToPage(context, 4),
                ),
                
                const Divider(height: 32),
                
                // Tools section
                _buildSectionHeader('Tools'),
                _buildMenuItem(
                  icon: Icons.search,
                  title: 'Search Records',
                  onTap: () => _showSearchDialog(context),
                ),
                _buildMenuItem(
                  icon: Icons.filter_list,
                  title: 'Filter Options',
                  onTap: () => _showFilterDialog(context),
                ),
                _buildMenuItem(
                  icon: Icons.download,
                  title: 'Export Data',
                  onTap: () => _showExportDialog(context),
                ),
                
                const Divider(height: 32),
                
                // Sync section
                _buildSyncSection(context),
                
                const Divider(height: 32),
                
                // Settings section
                _buildSectionHeader('Settings'),
                _buildMenuItem(
                  icon: Icons.person_outline,
                  title: 'Profile',
                  onTap: () => _showProfile(context),
                ),
                _buildMenuItem(
                  icon: Icons.settings,
                  title: 'Preferences',
                  onTap: () => _showSettings(context),
                ),
                _buildMenuItem(
                  icon: Icons.help_outline,
                  title: 'Help & Support',
                  onTap: () => _showHelp(context),
                ),
              ],
            ),
          ),
          
          // Footer
          _buildFooter(context),
        ],
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return BlocBuilder<FirebaseAuthBloc, FirebaseAuthState>(
      builder: (context, state) {
        if (state is! FirebaseAuthSuccess) {
          return const SizedBox.shrink();
        }

        return Container(
          width: double.infinity,
          padding: const EdgeInsets.fromLTRB(16, 48, 16, 16),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [AppColors.primary, AppColors.primary.withOpacity(0.8)],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Avatar
              CircleAvatar(
                radius: 32,
                backgroundColor: Colors.white.withOpacity(0.2),
                child: Text(
                  state.user.displayName.isNotEmpty 
                      ? state.user.displayName[0].toUpperCase()
                      : 'U',
                  style: AppTextStyles.headlineMedium.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              const SizedBox(height: 16),
              
              // User info
              Text(
                state.user.displayName,
                style: AppTextStyles.titleLarge.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                state.user.email,
                style: AppTextStyles.bodyMedium.copyWith(
                  color: Colors.white.withOpacity(0.9),
                ),
              ),
              const SizedBox(height: 8),
              
              // Role badge
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  state.user.role.displayName,
                  style: AppTextStyles.bodySmall.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 8, 16, 8),
      child: Text(
        title,
        style: AppTextStyles.bodySmall.copyWith(
          color: AppColors.textSecondary,
          fontWeight: FontWeight.w600,
          letterSpacing: 0.5,
        ),
      ),
    );
  }

  Widget _buildMenuItem({
    required IconData icon,
    required String title,
    required VoidCallback onTap,
    Widget? trailing,
  }) {
    return ListTile(
      leading: Icon(
        icon,
        color: AppColors.textSecondary,
        size: 22,
      ),
      title: Text(
        title,
        style: AppTextStyles.bodyMedium.copyWith(
          color: AppColors.textPrimary,
        ),
      ),
      trailing: trailing,
      onTap: onTap,
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
    );
  }

  Widget _buildSyncSection(BuildContext context) {
    return BlocBuilder<MundaBloc, MundaState>(
      builder: (context, state) {
        if (state is OfflineSyncInProgress) {
          return Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Text(
                      'Syncing...',
                      style: AppTextStyles.bodyMedium.copyWith(
                        color: AppColors.primary,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                LinearProgressIndicator(
                  value: state.progress / 100,
                  backgroundColor: AppColors.primary.withOpacity(0.2),
                  valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
                ),
                const SizedBox(height: 4),
                Text(
                  '${state.syncedItems}/${state.totalItems} items',
                  style: AppTextStyles.bodySmall.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          );
        }

        return _buildMenuItem(
          icon: Icons.sync,
          title: 'Sync Data',
          onTap: () => _handleSync(context),
          trailing: BlocBuilder<MundaBloc, MundaState>(
            builder: (context, state) {
              // Show offline count if available
              return FutureBuilder<int>(
                future: _getOfflineCount(context),
                builder: (context, snapshot) {
                  if (snapshot.hasData && snapshot.data! > 0) {
                    return Container(
                      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                      decoration: BoxDecoration(
                        color: AppColors.warning,
                        borderRadius: BorderRadius.circular(10),
                      ),
                      child: Text(
                        '${snapshot.data}',
                        style: AppTextStyles.bodySmall.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    );
                  }
                  return const SizedBox.shrink();
                },
              );
            },
          ),
        );
      },
    );
  }

  Widget _buildFooter(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border(
          top: BorderSide(
            color: AppColors.outline,
            width: 1,
          ),
        ),
      ),
      child: Column(
        children: [
          // Sign out button
          SizedBox(
            width: double.infinity,
            child: OutlinedButton.icon(
              onPressed: () => _handleSignOut(context),
              icon: Icon(Icons.logout, color: AppColors.error),
              label: Text(
                'Sign Out',
                style: TextStyle(color: AppColors.error),
              ),
              style: OutlinedButton.styleFrom(
                side: BorderSide(color: AppColors.error),
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
            ),
          ),
          const SizedBox(height: 8),
          
          // Version info
          Text(
            'Munda Operations v1.0.0',
            style: AppTextStyles.bodySmall.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
        ],
      ),
    );
  }

  void _navigateToPage(BuildContext context, int pageIndex) {
    Navigator.of(context).pop(); // Close drawer
    // TODO: Navigate to specific page
  }

  void _showSearchDialog(BuildContext context) {
    Navigator.of(context).pop();
    // TODO: Show search dialog
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Search functionality coming soon')),
    );
  }

  void _showFilterDialog(BuildContext context) {
    Navigator.of(context).pop();
    // TODO: Show filter dialog
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Filter options coming soon')),
    );
  }

  void _showExportDialog(BuildContext context) {
    Navigator.of(context).pop();
    // TODO: Show export dialog
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Export functionality coming soon')),
    );
  }

  void _showProfile(BuildContext context) {
    Navigator.of(context).pop();
    // TODO: Navigate to profile page
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Profile page coming soon')),
    );
  }

  void _showSettings(BuildContext context) {
    Navigator.of(context).pop();
    // TODO: Navigate to settings page
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Settings page coming soon')),
    );
  }

  void _showHelp(BuildContext context) {
    Navigator.of(context).pop();
    // TODO: Navigate to help page
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Help page coming soon')),
    );
  }

  void _handleSync(BuildContext context) {
    Navigator.of(context).pop();
    
    final authState = context.read<FirebaseAuthBloc>().state;
    if (authState is FirebaseAuthSuccess) {
      context.read<MundaBloc>().add(
        SyncOfflineData(userId: authState.user.id),
      );
    }
  }

  Future<int> _getOfflineCount(BuildContext context) async {
    // TODO: Get actual offline count from repository
    return 0;
  }

  void _handleSignOut(BuildContext context) {
    Navigator.of(context).pop();
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Sign Out'),
        content: const Text('Are you sure you want to sign out?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              context.read<FirebaseAuthBloc>().add(const FirebaseAuthSignOut());
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.error,
              foregroundColor: Colors.white,
            ),
            child: const Text('Sign Out'),
          ),
        ],
      ),
    );
  }
}
