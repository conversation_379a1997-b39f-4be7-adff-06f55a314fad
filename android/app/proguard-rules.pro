# Add project specific ProGuard rules here.
# You can control the set of applied configuration files using the
# proguardFiles setting in build.gradle.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# If your project uses WebView with JS, uncomment the following
# and specify the fully qualified class name to the JavaScript interface
# class:
#-keepclassmembers class fqcn.of.javascript.interface.for.webview {
#   public *;
#}

# Uncomment this to preserve the line number information for
# debugging stack traces.
#-keepattributes SourceFile,LineNumberTable

# If you keep the line number information, uncomment this to
# hide the original source file name.
#-renamesourcefileattribute SourceFile

# Keep Firebase classes
-keep class com.google.firebase.** { *; }
-keep class com.google.android.gms.** { *; }

# Keep Firebase Analytics
-keep class com.google.firebase.analytics.** { *; }
-keep class com.google.android.gms.analytics.** { *; }

# Keep Firebase Auth
-keep class com.google.firebase.auth.** { *; }

# Keep Firestore
-keep class com.google.firebase.firestore.** { *; }

# Keep Crashlytics
-keep class com.google.firebase.crashlytics.** { *; }

# Keep Remote Config
-keep class com.google.firebase.remoteconfig.** { *; }

# Keep Google Play Services
-keep class com.google.android.gms.common.** { *; }
-keep class com.google.android.gms.auth.** { *; }

# Keep Flutter specific classes
-keep class io.flutter.** { *; }
-keep class io.flutter.plugins.** { *; }

# Keep your app's main classes
-keep class com.example.hm_collection.** { *; }

# Keep native methods
-keepclasseswithmembernames class * {
    native <methods>;
}

# Keep Parcelable classes
-keep class * implements android.os.Parcelable {
    public static final android.os.Parcelable$Creator *;
}

# Keep Serializable classes
-keepclassmembers class * implements java.io.Serializable {
    static final long serialVersionUID;
    private static final java.io.ObjectStreamField[] serialPersistentFields;
    private void writeObject(java.io.ObjectOutputStream);
    private void readObject(java.io.ObjectInputStream);
    java.lang.Object writeReplace();
    java.lang.Object readResolve();
}

# Keep R classes
-keep class **.R$* {
    public static <fields>;
}

# Keep BuildConfig
-keep class **.BuildConfig { *; }

# Keep enum values
-keepclassmembers enum * {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}

# Keep generic signatures
-keepattributes Signature

# Keep annotations
-keepattributes *Annotation*

# Keep inner classes
-keepattributes InnerClasses

# Keep synthetic methods
-keepattributes Synthetic

# Keep bridge methods
-keepattributes BridgeMethods

# Keep exceptions
-keepattributes Exceptions

# Keep local variable table
-keepattributes LocalVariableTable

# Keep line number table
-keepattributes LineNumberTable

# Keep source file
-keepattributes SourceFile

# Keep deprecated
-keepattributes Deprecated

# Keep native methods
-keepattributes NativeMethods

# Keep generic signatures
-keepattributes GenericSignature

# Keep enclosing method
-keepattributes EnclosingMethod

# Keep inner classes
-keepattributes InnerClasses

# Keep synthetic methods
-keepattributes Synthetic

# Keep bridge methods
-keepattributes BridgeMethods

# Keep exceptions
-keepattributes Exceptions

# Keep local variable table
-keepattributes LocalVariableTable

# Keep line number table
-keepattributes LineNumberTable

# Keep source file
-keepattributes SourceFile

# Keep deprecated
-keepattributes Deprecated

# Keep native methods
-keepattributes NativeMethods

# Keep generic signatures
-keepattributes GenericSignature

# Keep enclosing method
-keepattributes EnclosingMethod
