buildscript {
    ext.kotlin_version = '2.1.0'  // Updated to match dependency requirements
    ext.google_services_version = '4.4.1'  // Updated Google Services version for compatibility
    ext.google_play_services_version = '18.3.0'  // Google Play Services version
    
    repositories {
        google()
        mavenCentral()
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:8.6.0'  // Using AGP 8.6 to support compileSdk 35
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
        classpath "com.google.gms:google-services:$google_services_version"  // Compatible version
    }
}

allprojects {
    repositories {
        google()
        mavenCentral()
    }
}

rootProject.buildDir = "../build"
subprojects {
    project.buildDir = "${rootProject.buildDir}/${project.name}"
    
    // Ensure all subprojects use the same compile SDK and Java version
    afterEvaluate { project ->
        if (project.hasProperty('android')) {
            android {
                compileSdkVersion 35

                compileOptions {
                    sourceCompatibility JavaVersion.VERSION_1_8
                    targetCompatibility JavaVersion.VERSION_1_8
                }
            }
        }

        // Ensure consistent Kotlin JVM target for all projects
        project.tasks.withType(org.jetbrains.kotlin.gradle.tasks.KotlinCompile).configureEach {
            kotlinOptions {
                jvmTarget = "1.8"
            }
        }
    }
}

tasks.register("clean", Delete) {
    delete rootProject.buildDir
}
